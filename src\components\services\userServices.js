import apiClient from './apiClient';
import { resturls } from '../utils/apiurls';

const userServices = {
  resetPassword: (payload) => apiClient.post(resturls.changePassword, payload),
  getProfile: (role, userInfo) => {
    const profileFetchUrl =
      role !== 'superuser'
        ? `${resturls.profilefetch}`
        : `${resturls.profilefetch}?user_id=${userInfo?.userId || userInfo?.user_id}`;
    return apiClient.get(profileFetchUrl);
  },
  isTokenValid: async (token) => {
    const { msg } = await apiClient.post(resturls.checkTokenValidity, {
      token,
    });
    return msg === 'success';
  },
  getTicketStats: async () => await apiClient.get(resturls.userTicketStats),
  getCategoryWiseUsers: (userType) => apiClient.get(`${resturls.obtainCategoryWiseUser}?user_type=${userType}`),
};

export const getCategories = () => apiClient.get(resturls.obtainCategortList);

export const getBusinesses = () => apiClient.get(resturls.getBusinesses);

export default userServices;
