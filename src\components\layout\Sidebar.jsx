import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  ChevronRight, 
  ChevronDown, 
  Plus, 
  Home, 
  FileText, 
  BarChart3, 
  Settings,
  Building2,
  Ticket,
  Activity,
  Mail,
  FolderOpen
} from 'lucide-react';
import ls from 'local-storage';
import { useAuth } from '../../contexts/AuthContext';
import { decryptData } from '../utils/cryptoUtils';
import GlobalService from '../services/GlobalServices';
import { resturls } from '../utils/apiurls';
import SyncMailBtn from '../global/components/SyncMailBtn';
import defaultLogo from '../../assets/Images/bussiness.png';

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { 
    headerLogo, 
    userInfo, 
    setBusiness, 
    globSelectedBusiness, 
    setGlobSelectedBusiness, 
    roleType 
  } = useAuth();

  const [activeMenu, setActiveMenu] = useState('home');
  const [showBusinessList, setShowBusinessList] = useState(false);
  const [businessList, setBusinessList] = useState([]);
  const [selectedBusiness, setSelectedBusiness] = useState(globSelectedBusiness);
  const [adminCount, setAdminCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [expandedMenus, setExpandedMenus] = useState({});
  const [activeSubMenu, setActiveSubMenu] = useState(null);

  const roleEncrypted = ls.get('access_token')?.role;
  const role = roleEncrypted && decryptData(roleEncrypted);
  const matched = ['business_user', 'business_superuser', 'accountant', 'superuser'].includes(role);
  const isAdmin = role === 'superuser';

  // Configuration submenu
  const configSubmenuList = [
    { name: 'Sync Settings', key: 'sync-settings', url: '/sync-settings' },
    { name: 'Ledger Groups', key: 'ledger-groups', url: '/ledger-groups' },
    { name: 'Ledger', key: 'ledger', url: '/ledger' },
    { name: 'Ledger Type Mapping', key: 'ledger-type-mapping', url: '/ledger-type-mapping' },
    { name: 'Stock Item', key: 'stock-item', url: '/stock-item' },
    { name: 'Stock Item Mapping', key: 'stock-item-mapping', url: '/stock-item-mapping' },
    { name: 'Non Stock Item Ledger', key: 'non-stock-item-ledger', url: '/non-stock-item-ledger' },
    { name: 'Tax Ledger', key: 'tax-ledger', url: '/tax-ledger' },
    { name: 'Cost Center', key: 'cost-center', url: '/cost-center' },
  ];

  // Reports submenu
  const reportsSubmenuList = [
    { name: 'Revenue Report', key: 'revenue-report', url: '/revenue-report' },
    { name: 'Expense Report', key: 'expense-report', url: '/expense-report' },
    { name: 'Accounts/Ledger Report', key: 'accounts-ledger-report', url: '/ledger-report' },
    { name: 'Inventory Report', key: 'inventory-report', url: '/inventory-report' },
    { name: 'Total Receivable', key: 'receivable-report', url: '/receivable-report' },
    { name: 'Total Payable', key: 'accounts-payables', url: '/accounts-payables' },
    { name: 'Project Cash Flow', key: 'project-cash-flow', url: '/cash-flow' },
  ];

  // Menu configurations based on role
  const getMenuList = () => {
    if (isAdmin) {
      return [
        { name: 'Dashboard', key: 'dashboard', icon: <Home className="w-5 h-5" />, url: '/' },
        { name: 'Organisations', key: 'organisations', icon: <Building2 className="w-5 h-5" />, url: '/businessList' },
        { name: 'Ticket Management', key: 'ticketManagement', icon: <Ticket className="w-5 h-5" />, url: '/ticketManagement' },
        { name: 'System Activity', key: 'systemActivity', icon: <Activity className="w-5 h-5" />, url: '/SystemActivityPage' },
        { name: 'Email Server', key: 'emailServer', icon: <Mail className="w-5 h-5" />, url: '/EmailServerPage' },
        { name: 'File Management', key: 'fileManagement', icon: <FolderOpen className="w-5 h-5" />, url: '/FileManagement' },
      ];
    }

    const features = selectedBusiness?.features || [];
    const isReportsEnabled = features.some((feature) => feature.name === 'Reports' && feature.is_active);

    return [
      { name: 'Home', key: 'home', icon: <Home className="w-5 h-5" />, url: '/' },
      { name: 'Invoices', key: 'invoices', icon: <FileText className="w-5 h-5" />, url: '/invoices' },
      { 
        name: 'Reports', 
        key: 'reports', 
        icon: <BarChart3 className="w-5 h-5" />, 
        submenu: isReportsEnabled ? reportsSubmenuList : null 
      },
      { 
        name: 'Configuration', 
        key: 'configuration', 
        icon: <Settings className="w-5 h-5" />, 
        submenu: configSubmenuList 
      },
    ];
  };

  const [menuList, setMenuList] = useState(getMenuList());

  // Fetch business list and admin count
  useEffect(() => {
    const userId = userInfo?.user_id || userInfo?.userId;
    if (userId) {
      setIsLoading(true);
      const delayCall = setTimeout(() => {
        GlobalService.generalSelect(
          (respdata) => {
            const { results } = respdata;
            setBusinessList(results);
            setBusiness(results);
            const selectedInLs = ls.get('selectedBusiness');
            setIsLoading(false);
            
            if (results?.length > 0) {
              const matchingBusiness = results.find((business) => business?.business_image === headerLogo);
              
              if (selectedInLs) {
                setSelectedBusiness(selectedInLs);
              } else {
                if (matchingBusiness) {
                  setSelectedBusiness(matchingBusiness);
                } else {
                  setSelectedBusiness(results[0]);
                  ls.set('selectedBusiness', results[0]);
                }
              }
            }
          },
          `${resturls.getBusinesses}`,
          {},
          'GET'
        );
      }, 100);
      return () => clearTimeout(delayCall);
    }
  }, []);

  // Fetch admin count for superuser
  useEffect(() => {
    if (isAdmin) {
      GlobalService.generalSelect(
        (respdata) => {
          const { data } = respdata;
          setAdminCount(data?.length || 0);
        },
        `${resturls.obtainCategoryWiseUser}?user_type=superusers`,
        {},
        'GET'
      );
    }
  }, [isAdmin]);

  // Update menu list when business or role changes
  useEffect(() => {
    setMenuList(getMenuList());
  }, [selectedBusiness, role]);

  // Set active menu based on current path
  useEffect(() => {
    const currentPath = location.pathname;
    if (currentPath === '/') {
      setActiveMenu(isAdmin ? 'dashboard' : 'home');
      setActiveSubMenu(null);
    } else if (currentPath.includes('/createTicket')) {
      setActiveMenu(null);
      setActiveSubMenu(null);
    } else {
      let activeItem = menuList.find((item) => item.url && currentPath.startsWith(item.url) && item.url !== '/');

      if (!activeItem) {
        menuList.forEach((menu) => {
          if (menu.submenu) {
            const subItem = menu.submenu.find((sub) => currentPath === sub.url);
            if (subItem) {
              activeItem = { key: menu.key };
              setActiveSubMenu(subItem.key);
              setExpandedMenus(prev => ({ ...prev, [menu.key]: true }));
            }
          }
        });
      }

      if (activeItem) {
        setActiveMenu(activeItem.key);
      } else {
        setActiveMenu(null);
      }
    }
  }, [location.pathname, menuList]);

  // Handle business change
  const handleBusinessChange = (business) => {
    setSelectedBusiness(business);
    ls.set('selectedBusiness', business);
    ls.set('globSelectedBusiness', business);
    setGlobSelectedBusiness(business);
    setShowBusinessList(false);
    
    // Clear cached data
    const keysToRemove = [
      'revenueDetails', 'revenueTrendLineDetails', 'revenueDetailedReport',
      'CostReportPayload', 'CostDetailsTrendlineData', 'details',
      'inventoryDetails', 'payableData', 'customerData', 'invoiceData',
      'projectCashFLow', 'receivableDetails', 'receivableData',
      'Cost Report Page_logs', 'ledgerTimeline', 'CostDetailedReport',
      'Revenue Report Page_logs', 'activeAccountList', 'ledgerReportList',
      'overviewInfo', 'transactionInfo', 'cashFlowData', 'totalIncome', 'totalExpense'
    ];
    keysToRemove.forEach((key) => ls.remove(key));

    if (location.pathname.includes('ticketList')) {
      navigate(`/ticketList/${roleType === 'user' ? business.business_id : ''}`);
    } else if (window.location.pathname === '/') {
      window.location.reload();
    }
  };

  // Handle menu click
  const handleMenuClick = (item) => {
    if (item.submenu) {
      setExpandedMenus(prev => ({ ...prev, [item.key]: !prev[item.key] }));
    } else if (item.url && !isLoading) {
      if (item.key === 'ticketList') {
        navigate(`/ticketList/${roleType === 'user' ? selectedBusiness?.business_id || userInfo?.business_id : ''}`);
      } else {
        navigate(item.url);
      }
    }
  };

  // Handle submenu click
  const handleSubMenuClick = (subItem) => {
    setActiveSubMenu(subItem.key);
    navigate(subItem.url);
  };

  const businessOptions = businessList?.map((business, index) => ({
    key: index,
    text: business.business_name,
    value: business,
    logo: business?.business_image,
  }));

  return (
    <div className="w-64 bg-white border-r border-gray-200 h-full flex flex-col">
      {/* Business Selector */}
      {matched && (
        <div 
          className="p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={() => setShowBusinessList(!showBusinessList)}
        >
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center flex-shrink-0">
              {selectedBusiness?.business_image ? (
                <img src={selectedBusiness?.business_image} alt="" className="w-full h-full object-cover" />
              ) : (
                <div className="w-6 h-6 bg-gray-400 rounded" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              {isLoading ? (
                <div className="animate-pulse bg-gray-200 h-4 rounded w-3/4" />
              ) : (
                <p className="text-sm font-medium text-gray-900 truncate">
                  {selectedBusiness?.business_name}
                </p>
              )}
            </div>
            <ChevronRight className={`w-4 h-4 text-gray-400 transition-transform ${showBusinessList ? 'rotate-90' : ''}`} />
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="p-4 space-y-3 border-b border-gray-100">
        <button
          onClick={() => navigate('/create-invoice')}
          className="w-full flex items-center justify-center gap-2 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
        >
          <Plus className="w-4 h-4" />
          New Invoice
        </button>
        <div className="w-full px-4 -mx-4">
          <SyncMailBtn />
        </div>
        {role === 'superuser' && (
          <button
            onClick={() => navigate('/businessCreation')}
            className="w-full flex items-center justify-center gap-2 px-4 py-2.5 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
          >
            <Plus className="w-4 h-4" />
            Create Business
          </button>
        )}
      </div>

      {/* Admin Info */}
      {isAdmin && (
        <div className="p-4 border-b border-gray-100">
          <div className="text-center">
            <p className="font-semibold text-gray-900">ArthTattva</p>
            <p className="text-sm text-gray-500">{adminCount} Admin Members</p>
          </div>
        </div>
      )}

      {/* Navigation Menu */}
      <nav className="flex-1 overflow-y-auto p-2">
        <ul className="space-y-1">
          {menuList.map((item) => (
            <li key={item.key}>
              {item.submenu ? (
                <div>
                  <button
                    onClick={() => handleMenuClick(item)}
                    className={`w-full flex items-center justify-between px-3 py-2 text-left rounded-lg transition-colors ${
                      activeMenu === item.key ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      {item.icon}
                      <span className="font-medium">{item.name}</span>
                    </div>
                    <ChevronDown className={`w-4 h-4 transition-transform ${expandedMenus[item.key] ? 'rotate-180' : ''}`} />
                  </button>
                  {expandedMenus[item.key] && (
                    <ul className="mt-1 ml-6 space-y-1">
                      {item.submenu.map((subItem) => (
                        <li key={subItem.key}>
                          <button
                            onClick={() => handleSubMenuClick(subItem)}
                            className={`w-full text-left px-3 py-2 text-sm rounded-lg transition-colors ${
                              activeSubMenu === subItem.key ? 'bg-blue-50 text-blue-700' : 'text-gray-600 hover:bg-gray-100'
                            }`}
                          >
                            {subItem.name}
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ) : (
                <button
                  onClick={() => handleMenuClick(item)}
                  className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors ${
                    activeMenu === item.key ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {item.icon}
                  <span className="font-medium">{item.name}</span>
                </button>
              )}
            </li>
          ))}
        </ul>
      </nav>

      {/* Business List Overlay */}
      {showBusinessList && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setShowBusinessList(false)}
          />
          <div className="absolute top-0 left-0 w-full h-full bg-white z-20 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h3 className="font-semibold text-gray-900">
                Your Organisations ({businessOptions?.length})
              </h3>
            </div>
            <div className="flex-1 overflow-y-auto p-2">
              <div className="space-y-1">
                {businessOptions?.map((item) => (
                  <button
                    key={item.key}
                    onClick={() => handleBusinessChange(item.value)}
                    className={`w-full flex items-center gap-3 p-3 text-left rounded-lg transition-colors ${
                      selectedBusiness?.business_name === item.text ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-100'
                    }`}
                  >
                    <div className="w-8 h-8 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center flex-shrink-0">
                      {item.logo ? (
                        <img src={item.logo} alt="" className="w-full h-full object-cover" />
                      ) : (
                        <div className="w-4 h-4 bg-gray-400 rounded" />
                      )}
                    </div>
                    <span className="truncate">{item.text}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Sidebar;
