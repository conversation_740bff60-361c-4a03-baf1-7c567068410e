import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Paper,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Button,
  Avatar,
  Typography,
  Divider,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  ChevronRight,
  ExpandMore,
  Add as Plus,
  Home,
  Description as FileText,
  BarChart as BarChart3,
  Settings,
  Business as Building2,
  ConfirmationNumber as Ticket,
  Timeline as Activity,
  Email as Mail,
  Folder as FolderOpen,
} from '@mui/icons-material';
import ls from 'local-storage';
import { useAuth } from '../../contexts/AuthContext';
import { decryptData } from '../utils/cryptoUtils';
import { useQuery } from '@tanstack/react-query';
import businessServices from '../services/businessServices';
import userServices from '../services/userServices';
import SyncMailBtn from '../global/components/SyncMailBtn';
import defaultLogo from '../../assets/Images/bussiness.png';

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { headerLogo, userInfo, setBusiness, globSelectedBusiness, setGlobSelectedBusiness, roleType } = useAuth();

  const [activeMenu, setActiveMenu] = useState('home');
  const [showBusinessList, setShowBusinessList] = useState(false);
  const [businessList, setBusinessList] = useState([]);
  const [selectedBusiness, setSelectedBusiness] = useState(globSelectedBusiness);
  const [adminCount, setAdminCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [expandedMenus, setExpandedMenus] = useState({});
  const [activeSubMenu, setActiveSubMenu] = useState(null);

  const roleEncrypted = ls.get('access_token')?.role;
  const role = roleEncrypted && decryptData(roleEncrypted);
  const matched = ['business_user', 'business_superuser', 'accountant', 'superuser'].includes(role);
  const isAdmin = role === 'superuser';

  // Configuration submenu
  const configSubmenuList = [
    { name: 'Sync Settings', key: 'sync-settings', url: '/sync-settings' },
    { name: 'Ledger Groups', key: 'ledger-groups', url: '/ledger-groups' },
    { name: 'Ledger', key: 'ledger', url: '/ledger' },
    { name: 'Ledger Type Mapping', key: 'ledger-type-mapping', url: '/ledger-type-mapping' },
    { name: 'Stock Item', key: 'stock-item', url: '/stock-item' },
    { name: 'Stock Item Mapping', key: 'stock-item-mapping', url: '/stock-item-mapping' },
    { name: 'Non Stock Item Ledger', key: 'non-stock-item-ledger', url: '/non-stock-item-ledger' },
    { name: 'Tax Ledger', key: 'tax-ledger', url: '/tax-ledger' },
    { name: 'Cost Center', key: 'cost-center', url: '/cost-center' },
  ];

  // Reports submenu
  const reportsSubmenuList = [
    { name: 'Revenue Report', key: 'revenue-report', url: '/revenue-report' },
    { name: 'Expense Report', key: 'expense-report', url: '/expense-report' },
    { name: 'Accounts/Ledger Report', key: 'accounts-ledger-report', url: '/ledger-report' },
    { name: 'Inventory Report', key: 'inventory-report', url: '/inventory-report' },
    { name: 'Total Receivable', key: 'receivable-report', url: '/receivable-report' },
    { name: 'Total Payable', key: 'accounts-payables', url: '/accounts-payables' },
    { name: 'Project Cash Flow', key: 'project-cash-flow', url: '/cash-flow' },
  ];

  // Menu configurations based on role
  const getMenuList = () => {
    if (isAdmin) {
      return [
        { name: 'Dashboard', key: 'dashboard', icon: <Home />, url: '/' },
        { name: 'Organizations', key: 'organisations', icon: <Building2 />, url: '/businessList' },
        { name: 'Ticket Management', key: 'ticketManagement', icon: <Ticket />, url: '/ticketManagement' },
        { name: 'System Activity', key: 'systemActivity', icon: <Activity />, url: '/SystemActivityPage' },
        { name: 'Email Server', key: 'emailServer', icon: <Mail />, url: '/EmailServerPage' },
        { name: 'File Management', key: 'fileManagement', icon: <FolderOpen />, url: '/FileManagement' },
      ];
    }

    const features = selectedBusiness?.features || [];
    const isReportsEnabled = features.some((feature) => feature.name === 'Reports' && feature.is_active);

    return [
      { name: 'Home', key: 'home', icon: <Home />, url: '/' },
      { name: 'Invoices', key: 'invoices', icon: <FileText />, url: '/invoices' },
      {
        name: 'Reports',
        key: 'reports',
        icon: <BarChart3 />,
        submenu: isReportsEnabled ? reportsSubmenuList : null,
      },
      {
        name: 'Configuration',
        key: 'configuration',
        icon: <Settings />,
        submenu: configSubmenuList,
      },
    ];
  };

  const [menuList, setMenuList] = useState(getMenuList());

  // Fetch business list using React Query
  const { data: businessData, isLoading: businessLoading } = useQuery({
    queryKey: ['businesses'],
    queryFn: businessServices.getBusinesses,
    enabled: !!(userInfo?.user_id || userInfo?.userId),
  });

  // Fetch admin count for superuser using React Query
  const { data: adminData } = useQuery({
    queryKey: ['admin-count'],
    queryFn: () => userServices.getCategoryWiseUsers('superusers'),
    enabled: isAdmin,
  });

  useEffect(() => {
    if (businessData?.results) {
      const results = businessData.results;
      setBusinessList(results);
      setBusiness(results);
      const selectedInLs = ls.get('selectedBusiness');

      if (results?.length > 0) {
        const matchingBusiness = results.find((business) => business?.business_image === headerLogo);

        if (selectedInLs) {
          setSelectedBusiness(selectedInLs);
        } else {
          if (matchingBusiness) {
            setSelectedBusiness(matchingBusiness);
          } else {
            setSelectedBusiness(results[0]);
            ls.set('selectedBusiness', results[0]);
          }
        }
      }
    }
  }, [businessData, headerLogo, setBusiness]);

  useEffect(() => {
    if (adminData?.data) {
      setAdminCount(adminData.data.length || 0);
    }
  }, [adminData]);

  useEffect(() => {
    setIsLoading(businessLoading);
  }, [businessLoading]);

  // Update menu list when business or role changes
  useEffect(() => {
    setMenuList(getMenuList());
  }, [selectedBusiness, role]);

  // Set active menu based on current path
  useEffect(() => {
    const currentPath = location.pathname;
    if (currentPath === '/') {
      setActiveMenu(isAdmin ? 'dashboard' : 'home');
      setActiveSubMenu(null);
    } else if (currentPath.includes('/createTicket')) {
      setActiveMenu(null);
      setActiveSubMenu(null);
    } else {
      let activeItem = menuList.find((item) => item.url && currentPath.startsWith(item.url) && item.url !== '/');

      if (!activeItem) {
        menuList.forEach((menu) => {
          if (menu.submenu) {
            const subItem = menu.submenu.find((sub) => currentPath === sub.url);
            if (subItem) {
              activeItem = { key: menu.key };
              setActiveSubMenu(subItem.key);
              setExpandedMenus((prev) => ({ ...prev, [menu.key]: true }));
            }
          }
        });
      }

      if (activeItem) {
        setActiveMenu(activeItem.key);
      } else {
        setActiveMenu(null);
      }
    }
  }, [location.pathname, menuList]);

  // Handle business change
  const handleBusinessChange = (business) => {
    setSelectedBusiness(business);
    ls.set('selectedBusiness', business);
    ls.set('globSelectedBusiness', business);
    setGlobSelectedBusiness(business);
    setShowBusinessList(false);

    // Clear cached data
    const keysToRemove = [
      'revenueDetails',
      'revenueTrendLineDetails',
      'revenueDetailedReport',
      'CostReportPayload',
      'CostDetailsTrendlineData',
      'details',
      'inventoryDetails',
      'payableData',
      'customerData',
      'invoiceData',
      'projectCashFLow',
      'receivableDetails',
      'receivableData',
      'Cost Report Page_logs',
      'ledgerTimeline',
      'CostDetailedReport',
      'Revenue Report Page_logs',
      'activeAccountList',
      'ledgerReportList',
      'overviewInfo',
      'transactionInfo',
      'cashFlowData',
      'totalIncome',
      'totalExpense',
    ];
    keysToRemove.forEach((key) => ls.remove(key));

    if (location.pathname.includes('ticketList')) {
      navigate(`/ticketList/${roleType === 'user' ? business.business_id : ''}`);
    } else if (window.location.pathname === '/') {
      window.location.reload();
    }
  };

  // Handle menu click
  const handleMenuClick = (item) => {
    if (item.submenu) {
      setExpandedMenus((prev) => ({ ...prev, [item.key]: !prev[item.key] }));
    } else if (item.url && !isLoading) {
      if (item.key === 'ticketList') {
        navigate(`/ticketList/${roleType === 'user' ? selectedBusiness?.business_id || userInfo?.business_id : ''}`);
      } else {
        navigate(item.url);
      }
    }
  };

  // Handle submenu click
  const handleSubMenuClick = (subItem) => {
    setActiveSubMenu(subItem.key);
    navigate(subItem.url);
  };

  const businessOptions = businessList?.map((business, index) => ({
    key: index,
    text: business.business_name,
    value: business,
    logo: business?.business_image,
  }));

  return (
    <Paper
      elevation={1}
      sx={{
        width: 257,
        height: '100%',
        borderRadius: 2,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}
    >
      {/* Business Selector */}
      {matched && (
        <ListItem button onClick={() => setShowBusinessList(!showBusinessList)} sx={{ p: 2 }}>
          <Avatar
            src={selectedBusiness?.business_image || defaultLogo}
            sx={{
              width: 40,
              height: 40,
              mr: 2,
              border: '1px solid #e0e6ed',
            }}
          >
            <Building2 />
          </Avatar>
          <ListItemText
            primary={
              isLoading ? (
                <CircularProgress size={16} />
              ) : (
                <Typography variant="body2" fontWeight="medium" noWrap>
                  {selectedBusiness?.business_name}
                </Typography>
              )
            }
          />
          <ChevronRight
            sx={{
              transform: showBusinessList ? 'rotate(90deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s',
            }}
          />
        </ListItem>
      )}

      {/* Action Buttons */}
      <Box sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Button
          variant="contained"
          startIcon={<Plus />}
          onClick={() => navigate('/create-invoice')}
          sx={{
            borderRadius: 25,
            backgroundColor: '#4ac2ff1a',
            color: '#4ac2ff',
            fontWeight: 'bold',
            '&:hover': {
              backgroundColor: '#4ac2ff33',
            },
          }}
        >
          New Invoice
        </Button>

        <Box sx={{ mx: -1 }}>
          <SyncMailBtn />
        </Box>

        {role === 'superuser' && (
          <Button
            variant="contained"
            startIcon={<Plus />}
            onClick={() => navigate('/businessCreation')}
            sx={{
              borderRadius: 25,
              backgroundColor: '#5cd68a1a',
              color: '#5cd68a',
              fontWeight: 'bold',
              '&:hover': {
                backgroundColor: '#5cd68a33',
              },
            }}
          >
            Create Business
          </Button>
        )}
      </Box>

      {/* Admin Info */}
      {isAdmin && (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="h6" fontWeight="bold">
            ArthTattva
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {adminCount} Admin Members
          </Typography>
        </Box>
      )}

      <Divider />

      {/* Navigation Menu */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <List sx={{ p: 1 }}>
          {menuList.map((item) => (
            <React.Fragment key={item.key}>
              {item.submenu ? (
                <Accordion
                  expanded={expandedMenus[item.key] || false}
                  onChange={() => handleMenuClick(item)}
                  elevation={0}
                  sx={{
                    '&:before': { display: 'none' },
                    boxShadow: 'none',
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMore />}
                    sx={{
                      backgroundColor: activeMenu === item.key ? '#4ac2ff1a' : 'transparent',
                      color: activeMenu === item.key ? '#4ac2ff' : 'inherit',
                      borderRadius: 1,
                      '&:hover': {
                        backgroundColor: '#f5f5f5',
                      },
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>
                    <Typography variant="body1" fontWeight="medium">
                      {item.name}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails sx={{ p: 0 }}>
                    <List sx={{ pl: 4 }}>
                      {item.submenu.map((subItem) => (
                        <ListItem key={subItem.key} disablePadding>
                          <ListItemButton
                            onClick={() => handleSubMenuClick(subItem)}
                            sx={{
                              backgroundColor: activeSubMenu === subItem.key ? '#4ac2ff1a' : 'transparent',
                              color: activeSubMenu === subItem.key ? '#4ac2ff' : 'inherit',
                              borderRadius: 1,
                              '&:hover': {
                                backgroundColor: '#f5f5f5',
                              },
                            }}
                          >
                            <ListItemText primary={subItem.name} primaryTypographyProps={{ variant: 'body2' }} />
                          </ListItemButton>
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              ) : (
                <ListItem disablePadding>
                  <ListItemButton
                    onClick={() => handleMenuClick(item)}
                    sx={{
                      backgroundColor: activeMenu === item.key ? '#4ac2ff1a' : 'transparent',
                      color: activeMenu === item.key ? '#4ac2ff' : 'inherit',
                      borderRadius: 1,
                      borderRight: activeMenu === item.key ? '4px solid #4ac2ff' : 'none',
                      '&:hover': {
                        backgroundColor: '#f5f5f5',
                      },
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>
                    <ListItemText
                      primary={item.name}
                      primaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
                    />
                  </ListItemButton>
                </ListItem>
              )}
            </React.Fragment>
          ))}
        </List>
      </Box>

      {/* Business List Overlay */}
      {showBusinessList && (
        <>
          <Box
            sx={{
              position: 'fixed',
              inset: 0,
              zIndex: 10,
            }}
            onClick={() => setShowBusinessList(false)}
          />
          <Paper
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              zIndex: 20,
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6" fontWeight="medium">
                Your Organizations ({businessOptions?.length})
              </Typography>
            </Box>
            <Box sx={{ flex: 1, overflow: 'auto' }}>
              <List>
                {businessOptions?.map((item) => (
                  <ListItem key={item.key} disablePadding>
                    <ListItemButton
                      onClick={() => handleBusinessChange(item.value)}
                      sx={{
                        backgroundColor: selectedBusiness?.business_name === item.text ? '#4ac2ff1a' : 'transparent',
                        color: selectedBusiness?.business_name === item.text ? '#4ac2ff' : 'inherit',
                        '&:hover': {
                          backgroundColor: '#f5f5f5',
                        },
                      }}
                    >
                      <Avatar
                        src={item.logo}
                        sx={{
                          width: 32,
                          height: 32,
                          mr: 2,
                          border: '1px solid #e0e6ed',
                        }}
                      >
                        <Building2 />
                      </Avatar>
                      <ListItemText primary={item.text} primaryTypographyProps={{ noWrap: true }} />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            </Box>
          </Paper>
        </>
      )}
    </Paper>
  );
};

export default Sidebar;
