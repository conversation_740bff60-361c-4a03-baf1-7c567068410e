import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Paper,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Button,
  Typography,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  ExpandMore,
  Add as Plus,
  Home,
  Description as FileText,
  BarChart as BarChart3,
  Settings,
  Business as Building2,
  ConfirmationNumber as Ticket,
  Timeline as Activity,
  Email as Mail,
  Folder as FolderOpen,
} from '@mui/icons-material';
import ls from 'local-storage';
import { useAuth } from '../../contexts/AuthContext';
import { decryptData } from '../utils/cryptoUtils';
import { useQuery } from '@tanstack/react-query';
import userServices from '../services/userServices';
import SyncMailBtn from '../global/components/SyncMailBtn';


const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { userInfo, roleType } = useAuth();

  const [activeMenu, setActiveMenu] = useState('home');
  const [adminCount, setAdminCount] = useState(0);
  const [expandedMenus, setExpandedMenus] = useState({});
  const [activeSubMenu, setActiveSubMenu] = useState(null);

  const roleEncrypted = ls.get('access_token')?.role;
  const role = roleEncrypted && decryptData(roleEncrypted);

  const isAdmin = role === 'superuser';

  // Menu configurations based on role
  const getMenuList = useCallback(() => {
    // Configuration submenu
    const configSubmenuList = [
      { name: 'Sync Settings', key: 'sync-settings', url: '/sync-settings' },
      { name: 'Ledger Groups', key: 'ledger-groups', url: '/ledger-groups' },
      { name: 'Ledger', key: 'ledger', url: '/ledger' },
      { name: 'Ledger Type Mapping', key: 'ledger-type-mapping', url: '/ledger-type-mapping' },
      { name: 'Stock Item', key: 'stock-item', url: '/stock-item' },
      { name: 'Stock Item Mapping', key: 'stock-item-mapping', url: '/stock-item-mapping' },
      { name: 'Non Stock Item Ledger', key: 'non-stock-item-ledger', url: '/non-stock-item-ledger' },
      { name: 'Tax Ledger', key: 'tax-ledger', url: '/tax-ledger' },
      { name: 'Cost Center', key: 'cost-center', url: '/cost-center' },
    ];

    // Reports submenu
    const reportsSubmenuList = [
      { name: 'Revenue Report', key: 'revenue-report', url: '/revenue-report' },
      { name: 'Expense Report', key: 'expense-report', url: '/expense-report' },
      { name: 'Accounts/Ledger Report', key: 'accounts-ledger-report', url: '/ledger-report' },
      { name: 'Inventory Report', key: 'inventory-report', url: '/inventory-report' },
      { name: 'Total Receivable', key: 'receivable-report', url: '/receivable-report' },
      { name: 'Total Payable', key: 'accounts-payables', url: '/accounts-payables' },
      { name: 'Project Cash Flow', key: 'project-cash-flow', url: '/cash-flow' },
    ];
    if (isAdmin) {
      return [
        { name: 'Dashboard', key: 'dashboard', icon: <Home />, url: '/' },
        { name: 'Organizations', key: 'organisations', icon: <Building2 />, url: '/businessList' },
        { name: 'Ticket Management', key: 'ticketManagement', icon: <Ticket />, url: '/ticketManagement' },
        { name: 'System Activity', key: 'systemActivity', icon: <Activity />, url: '/SystemActivityPage' },
        { name: 'Email Server', key: 'emailServer', icon: <Mail />, url: '/EmailServerPage' },
        { name: 'File Management', key: 'fileManagement', icon: <FolderOpen />, url: '/FileManagement' },
      ];
    }

    // For non-admin users, get selected business from localStorage
    const selectedBusiness = ls.get('selectedBusiness');
    const features = selectedBusiness?.features || [];
    const isReportsEnabled = features.some((feature) => feature.name === 'Reports' && feature.is_active);

    return [
      { name: 'Home', key: 'home', icon: <Home />, url: '/' },
      { name: 'Invoices', key: 'invoices', icon: <FileText />, url: '/invoices' },
      {
        name: 'Reports',
        key: 'reports',
        icon: <BarChart3 />,
        submenu: isReportsEnabled ? reportsSubmenuList : null,
      },
      {
        name: 'Configuration',
        key: 'configuration',
        icon: <Settings />,
        submenu: configSubmenuList,
      },
    ];
  }, [isAdmin]);

  const [menuList, setMenuList] = useState(getMenuList());



  // Fetch admin count for superuser using React Query
  const { data: adminData } = useQuery({
    queryKey: ['admin-count'],
    queryFn: () => userServices.getCategoryWiseUsers('superusers'),
    enabled: isAdmin,
  });



  useEffect(() => {
    if (adminData?.data) {
      setAdminCount(adminData.data.length || 0);
    }
  }, [adminData]);



  // Update menu list when role changes
  useEffect(() => {
    setMenuList(getMenuList());
  }, [getMenuList]);

  // Set active menu based on current path
  useEffect(() => {
    const currentPath = location.pathname;
    if (currentPath === '/') {
      setActiveMenu(isAdmin ? 'dashboard' : 'home');
      setActiveSubMenu(null);
    } else if (currentPath.includes('/createTicket')) {
      setActiveMenu(null);
      setActiveSubMenu(null);
    } else {
      let activeItem = menuList.find((item) => item.url && currentPath.startsWith(item.url) && item.url !== '/');

      if (!activeItem) {
        menuList.forEach((menu) => {
          if (menu.submenu) {
            const subItem = menu.submenu.find((sub) => currentPath === sub.url);
            if (subItem) {
              activeItem = { key: menu.key };
              setActiveSubMenu(subItem.key);
              setExpandedMenus((prev) => ({ ...prev, [menu.key]: true }));
            }
          }
        });
      }

      if (activeItem) {
        setActiveMenu(activeItem.key);
      } else {
        setActiveMenu(null);
      }
    }
  }, [location.pathname, menuList]);



  // Handle menu click
  const handleMenuClick = (item) => {
    if (item.submenu) {
      setExpandedMenus((prev) => ({ ...prev, [item.key]: !prev[item.key] }));
    } else if (item.url) {
      if (item.key === 'ticketList') {
        const selectedBusiness = ls.get('selectedBusiness');
        navigate(`/ticketList/${roleType === 'user' ? selectedBusiness?.business_id || userInfo?.business_id : ''}`);
      } else {
        navigate(item.url);
      }
    }
  };

  // Handle submenu click
  const handleSubMenuClick = (subItem) => {
    setActiveSubMenu(subItem.key);
    navigate(subItem.url);
  };



  return (
    <Paper
      elevation={1}
      sx={{
        width: 257,
        height: '100%',
        borderRadius: 2,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}
    >


      {/* Action Buttons */}
      <Box sx={{ p: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Button
          variant="contained"
          startIcon={<Plus />}
          onClick={() => navigate('/create-invoice')}
          sx={{
            borderRadius: 25,
            backgroundColor: '#4ac2ff1a',
            color: '#4ac2ff',
            fontWeight: 'bold',
            '&:hover': {
              backgroundColor: '#4ac2ff33',
            },
          }}
        >
          New Invoice
        </Button>

        <Box sx={{ mx: -1 }}>
          <SyncMailBtn />
        </Box>

        {role === 'superuser' && (
          <Button
            variant="contained"
            startIcon={<Plus />}
            onClick={() => navigate('/businessCreation')}
            sx={{
              borderRadius: 25,
              backgroundColor: '#5cd68a1a',
              color: '#5cd68a',
              fontWeight: 'bold',
              '&:hover': {
                backgroundColor: '#5cd68a33',
              },
            }}
          >
            Create Business
          </Button>
        )}
      </Box>

      {/* Admin Info */}
      {isAdmin && (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="h6" fontWeight="bold">
            ArthTattva
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {adminCount} Admin Members
          </Typography>
        </Box>
      )}

      <Divider />

      {/* Navigation Menu */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <List sx={{ p: 1 }}>
          {menuList.map((item) => (
            <React.Fragment key={item.key}>
              {item.submenu ? (
                <Accordion
                  expanded={expandedMenus[item.key] || false}
                  onChange={() => handleMenuClick(item)}
                  elevation={0}
                  sx={{
                    '&:before': { display: 'none' },
                    boxShadow: 'none',
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMore />}
                    sx={{
                      backgroundColor: activeMenu === item.key ? '#4ac2ff1a' : 'transparent',
                      color: activeMenu === item.key ? '#4ac2ff' : 'inherit',
                      borderRadius: 1,
                      '&:hover': {
                        backgroundColor: '#f5f5f5',
                      },
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>
                    <Typography variant="body1" fontWeight="medium">
                      {item.name}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails sx={{ p: 0 }}>
                    <List sx={{ pl: 4 }}>
                      {item.submenu.map((subItem) => (
                        <ListItem key={subItem.key} disablePadding>
                          <ListItemButton
                            onClick={() => handleSubMenuClick(subItem)}
                            sx={{
                              backgroundColor: activeSubMenu === subItem.key ? '#4ac2ff1a' : 'transparent',
                              color: activeSubMenu === subItem.key ? '#4ac2ff' : 'inherit',
                              borderRadius: 1,
                              '&:hover': {
                                backgroundColor: '#f5f5f5',
                              },
                            }}
                          >
                            <ListItemText primary={subItem.name} primaryTypographyProps={{ variant: 'body2' }} />
                          </ListItemButton>
                        </ListItem>
                      ))}
                    </List>
                  </AccordionDetails>
                </Accordion>
              ) : (
                <ListItem disablePadding>
                  <ListItemButton
                    onClick={() => handleMenuClick(item)}
                    sx={{
                      backgroundColor: activeMenu === item.key ? '#4ac2ff1a' : 'transparent',
                      color: activeMenu === item.key ? '#4ac2ff' : 'inherit',
                      borderRadius: 1,
                      borderRight: activeMenu === item.key ? '4px solid #4ac2ff' : 'none',
                      '&:hover': {
                        backgroundColor: '#f5f5f5',
                      },
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>
                    <ListItemText
                      primary={item.name}
                      primaryTypographyProps={{ variant: 'body1', fontWeight: 'medium' }}
                    />
                  </ListItemButton>
                </ListItem>
              )}
            </React.Fragment>
          ))}
        </List>
      </Box>


    </Paper>
  );
};

export default Sidebar;
