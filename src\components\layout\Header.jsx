import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ChevronDown, Bell, X } from 'lucide-react';
import Avatar from 'react-avatar';
import ls from 'local-storage';
import { useAuth } from '../../contexts/AuthContext';
import { decryptData } from '../utils/cryptoUtils';
import { useQuery } from '@tanstack/react-query';
import businessServices from '../services/businessServices';
import Notification from '../screens/Notification';
import OmniSageAiLogo from '../../assets/Images/omnisage_ai_logo_transparent.png';
import defaultLogo from '../../assets/Images/bussiness.png';

const Header = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { 
    headerLogo, 
    userInfo, 
    hasNewNotification, 
    setBusiness, 
    isMobileScreen 
  } = useAuth();

  const [businessLogo, setBusinessLogo] = useState(headerLogo);
  const [businessList, setBusinessList] = useState([]);
  const [selectedBusiness, setSelectedBusiness] = useState();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [notificationActive, setNotificationActive] = useState(false);

  const userName = ls.get('userName');
  const roleEncrypted = ls.get('access_token')?.role;
  const role = roleEncrypted && decryptData(roleEncrypted);
  
  const currentPath = location.pathname;
  const isLoginOrForgetPassword = ['/login', '/forgetPassword', '/500', '/forgetPassword/reset'].includes(currentPath);
  const isHome = currentPath === '/';

  // Fetch business list using React Query
  const { data: businessData } = useQuery({
    queryKey: ['businesses'],
    queryFn: businessServices.getBusinesses,
    enabled: !!(userInfo?.user_id || userInfo?.userId),
  });

  useEffect(() => {
    if (businessData?.results) {
      const results = businessData.results;
      setBusinessList(results);
      setBusiness(results);
      const selectedInLs = ls.get('selectedBusiness');

      if (results?.length > 0) {
        const matchingBusiness = results.find((business) => business?.business_image === headerLogo);

        if (selectedInLs) {
          const businessLogo = selectedInLs?.business_image || defaultLogo;
          setBusinessLogo(businessLogo);
          setSelectedBusiness(selectedInLs);
        } else {
          if (matchingBusiness) {
            setSelectedBusiness(matchingBusiness);
            setBusinessLogo(matchingBusiness.business_image || defaultLogo);
          } else {
            const businessLogo = results[0]?.business_image || defaultLogo;
            setBusinessLogo(businessLogo);
            setSelectedBusiness(results[0]);
            ls.set('selectedBusiness', results[0]);
          }
        }
      }
    }
  }, [businessData, headerLogo, setBusiness]);

  // Handle business change
  const handleBusinessChange = (business) => {
    setSelectedBusiness(business);
    const headerlogo = business.business_image || defaultLogo;
    setBusinessLogo(headerlogo);
    navigate(`/ticketList/${business.business_id}`);
    ls.set('selectedBusiness', business);
    setDropdownOpen(false);
    
    // Clear cached data
    const keysToRemove = [
      'revenueDetails', 'revenueTrendLineDetails', 'revenueDetailedReport',
      'CostReportPayload', 'CostDetailsTrendlineData', 'details',
      'inventoryDetails', 'payableData', 'customerData', 'invoiceData',
      'projectCashFLow', 'receivableDetails', 'receivableData',
      'Cost Report Page_logs', 'ledgerTimeline', 'CostDetailedReport',
      'Revenue Report Page_logs', 'activeAccountList', 'ledgerReportList',
      'overviewInfo', 'transactionInfo', 'cashFlowData', 'totalIncome', 'totalExpense'
    ];
    keysToRemove.forEach((key) => ls.remove(key));

    if (window.location.pathname === '/') {
      window.location.reload();
    } else {
      navigate('/');
    }
  };

  const handleNotificationPanel = () => {
    if (isMobileScreen) {
      navigate('/notification');
    } else {
      setNotificationActive(true);
    }
  };

  const businessOptions = businessList?.map((business, index) => ({
    key: index,
    text: business.business_name,
    value: business,
    logo: business?.business_image,
  }));

  const renderBusinessDropdown = () => {
    if (businessList?.length === 0 || isHome || isLoginOrForgetPassword) return null;
    
    if (businessList?.length > 1) {
      return (
        <div className="relative">
          <button
            onClick={() => setDropdownOpen(!dropdownOpen)}
            className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
          >
            {selectedBusiness?.business_name}
            <ChevronDown className={`w-4 h-4 transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />
          </button>
          
          {dropdownOpen && (
            <>
              <div 
                className="fixed inset-0 z-10" 
                onClick={() => setDropdownOpen(false)}
              />
              <div className="absolute top-full left-0 mt-1 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-20">
                <div className="p-3 border-b border-gray-100">
                  <p className="text-sm font-medium text-gray-900">
                    Your Organisations ({businessOptions?.length})
                  </p>
                </div>
                <div className="max-h-64 overflow-y-auto">
                  {businessOptions?.map((item) => (
                    <button
                      key={item.key}
                      onClick={() => handleBusinessChange(item.value)}
                      className={`w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-gray-50 transition-colors ${
                        selectedBusiness?.business_name === item.text ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                      }`}
                    >
                      <div className="w-8 h-8 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
                        {item.logo ? (
                          <img src={item.logo} alt="" className="w-full h-full object-cover" />
                        ) : (
                          <div className="w-4 h-4 bg-gray-400 rounded" />
                        )}
                      </div>
                      <span className="truncate">{item.text}</span>
                    </button>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
      );
    } else {
      return (
        <span className="text-sm font-medium text-gray-700">
          {businessList[0]?.business_name}
        </span>
      );
    }
  };

  if (isLoginOrForgetPassword) {
    return (
      <header className="bg-white shadow-sm px-8 py-5">
        <div className="flex items-center justify-between">
          <button onClick={() => navigate('/')} className="flex items-center">
            <img src={OmniSageAiLogo} alt="OmniSageAi" className="h-8 w-auto" />
          </button>
        </div>
      </header>
    );
  }

  return (
    <>
      <header className="bg-white shadow-sm px-8 py-5 sticky top-0 z-30">
        <div className="flex items-center justify-between">
          {/* Logo and Business Selector */}
          <div className="flex items-center gap-6">
            <button onClick={() => navigate('/')} className="flex items-center">
              {isMobileScreen && !['accountant', 'manager'].includes(role) && !isHome ? (
                <img src={businessLogo} alt="" className="h-8 w-8 rounded-lg object-cover" />
              ) : (
                <img src={OmniSageAiLogo} alt="OmniSageAi" className="h-8 w-auto" />
              )}
            </button>

            {!isMobileScreen && renderBusinessDropdown()}
            {isMobileScreen && !['accountant', 'manager'].includes(role) && renderBusinessDropdown()}
          </div>

          {/* Right side - Notifications and Avatar */}
          <div className="flex items-center gap-4">
            {/* Notification Bell */}
            <button
              onClick={handleNotificationPanel}
              className="relative p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Bell className="w-5 h-5" />
              {hasNewNotification && (
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full" />
              )}
            </button>

            {/* User Avatar */}
            <button onClick={() => navigate('/profile')} className="flex items-center">
              <Avatar
                className="rounded-lg"
                color={isMobileScreen ? '#011638' : '#293056'}
                name={userName || 'User'}
                size="40"
              />
            </button>
          </div>
        </div>
      </header>

      {/* Notification Modal */}
      {notificationActive && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg w-full max-w-2xl h-[85vh] flex flex-col">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Notifications</h2>
              <button
                onClick={() => setNotificationActive(false)}
                className="p-1 text-gray-400 hover:text-gray-600 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="flex-1 overflow-hidden">
              <Notification setNoticationActive={setNotificationActive} />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Header;
