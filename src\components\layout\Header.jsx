import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Box,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Avatar as MuiAvatar,
  Badge,
  Modal,
  Paper,
  List,
  ListItem,
  ListItemButton,
  ListItemAvatar,
  ListItemText
} from '@mui/material';
import {
  KeyboardArrowDown,
  Notifications,
  Close,
  Business
} from '@mui/icons-material';
import Avatar from 'react-avatar';
import ls from 'local-storage';
import { useAuth } from '../../contexts/AuthContext';
import { decryptData } from '../utils/cryptoUtils';
import { useQuery } from '@tanstack/react-query';
import businessServices from '../services/businessServices';
import Notification from '../screens/Notification';
import OmniSageAiLogo from '../../assets/Images/omnisage_ai_logo_transparent.png';
import defaultLogo from '../../assets/Images/bussiness.png';

const Header = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { 
    headerLogo, 
    userInfo, 
    hasNewNotification, 
    setBusiness, 
    isMobileScreen 
  } = useAuth();

  const [businessLogo, setBusinessLogo] = useState(headerLogo);
  const [businessList, setBusinessList] = useState([]);
  const [selectedBusiness, setSelectedBusiness] = useState();
  const [dropdownAnchor, setDropdownAnchor] = useState(null);
  const [notificationActive, setNotificationActive] = useState(false);

  const dropdownOpen = Boolean(dropdownAnchor);

  const userName = ls.get('userName');
  const roleEncrypted = ls.get('access_token')?.role;
  const role = roleEncrypted && decryptData(roleEncrypted);
  
  const currentPath = location.pathname;
  const isLoginOrForgetPassword = ['/login', '/forgetPassword', '/500', '/forgetPassword/reset'].includes(currentPath);
  const isHome = currentPath === '/';

  // Fetch business list using React Query
  const { data: businessData } = useQuery({
    queryKey: ['businesses'],
    queryFn: businessServices.getBusinesses,
    enabled: !!(userInfo?.user_id || userInfo?.userId),
  });

  useEffect(() => {
    if (businessData?.results) {
      const results = businessData.results;
      setBusinessList(results);
      setBusiness(results);
      const selectedInLs = ls.get('selectedBusiness');

      if (results?.length > 0) {
        const matchingBusiness = results.find((business) => business?.business_image === headerLogo);

        if (selectedInLs) {
          const businessLogo = selectedInLs?.business_image || defaultLogo;
          setBusinessLogo(businessLogo);
          setSelectedBusiness(selectedInLs);
        } else {
          if (matchingBusiness) {
            setSelectedBusiness(matchingBusiness);
            setBusinessLogo(matchingBusiness.business_image || defaultLogo);
          } else {
            const businessLogo = results[0]?.business_image || defaultLogo;
            setBusinessLogo(businessLogo);
            setSelectedBusiness(results[0]);
            ls.set('selectedBusiness', results[0]);
          }
        }
      }
    }
  }, [businessData, headerLogo, setBusiness]);

  // Handle business change
  const handleBusinessChange = (business) => {
    setSelectedBusiness(business);
    const headerlogo = business.business_image || defaultLogo;
    setBusinessLogo(headerlogo);
    navigate(`/ticketList/${business.business_id}`);
    ls.set('selectedBusiness', business);
    setDropdownAnchor(null);
    
    // Clear cached data
    const keysToRemove = [
      'revenueDetails', 'revenueTrendLineDetails', 'revenueDetailedReport',
      'CostReportPayload', 'CostDetailsTrendlineData', 'details',
      'inventoryDetails', 'payableData', 'customerData', 'invoiceData',
      'projectCashFLow', 'receivableDetails', 'receivableData',
      'Cost Report Page_logs', 'ledgerTimeline', 'CostDetailedReport',
      'Revenue Report Page_logs', 'activeAccountList', 'ledgerReportList',
      'overviewInfo', 'transactionInfo', 'cashFlowData', 'totalIncome', 'totalExpense'
    ];
    keysToRemove.forEach((key) => ls.remove(key));

    if (window.location.pathname === '/') {
      window.location.reload();
    } else {
      navigate('/');
    }
  };

  const handleNotificationPanel = () => {
    if (isMobileScreen) {
      navigate('/notification');
    } else {
      setNotificationActive(true);
    }
  };

  const businessOptions = businessList?.map((business, index) => ({
    key: index,
    text: business.business_name,
    value: business,
    logo: business?.business_image,
  }));

  const renderBusinessDropdown = () => {
    if (businessList?.length === 0 || isHome || isLoginOrForgetPassword) return null;

    if (businessList?.length > 1) {
      return (
        <>
          <IconButton
            onClick={(e) => setDropdownAnchor(e.currentTarget)}
            sx={{ color: 'text.primary' }}
          >
            <Typography variant="body2" sx={{ mr: 1 }}>
              {selectedBusiness?.business_name}
            </Typography>
            <KeyboardArrowDown />
          </IconButton>

          <Menu
            anchorEl={dropdownAnchor}
            open={dropdownOpen}
            onClose={() => setDropdownAnchor(null)}
            PaperProps={{
              sx: { minWidth: 250, maxHeight: 300 }
            }}
          >
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="subtitle2" fontWeight="medium">
                Your Organizations ({businessOptions?.length})
              </Typography>
            </Box>
            {businessOptions?.map((item) => (
              <MenuItem
                key={item.key}
                onClick={() => handleBusinessChange(item.value)}
                selected={selectedBusiness?.business_name === item.text}
              >
                <ListItemAvatar>
                  <MuiAvatar
                    src={item.logo}
                    sx={{ width: 32, height: 32 }}
                  >
                    <Business />
                  </MuiAvatar>
                </ListItemAvatar>
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{ noWrap: true }}
                />
              </MenuItem>
            ))}
          </Menu>
        </>
      );
    } else {
      return (
        <Typography variant="body2" color="text.primary">
          {businessList[0]?.business_name}
        </Typography>
      );
    }
  };

  if (isLoginOrForgetPassword) {
    return (
      <AppBar position="static" elevation={1} sx={{ backgroundColor: 'white', color: 'text.primary' }}>
        <Toolbar>
          <IconButton onClick={() => navigate('/')} edge="start">
            <img src={OmniSageAiLogo} alt="OmniSageAi" style={{ height: 32 }} />
          </IconButton>
        </Toolbar>
      </AppBar>
    );
  }

  return (
    <>
      <AppBar position="sticky" elevation={1} sx={{ backgroundColor: 'white', color: 'text.primary' }}>
        <Toolbar>
          {/* Logo and Business Selector */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flex: 1 }}>
            <IconButton onClick={() => navigate('/')} edge="start">
              {isMobileScreen && !['accountant', 'manager'].includes(role) && !isHome ? (
                <MuiAvatar src={businessLogo} sx={{ width: 32, height: 32 }} />
              ) : (
                <img src={OmniSageAiLogo} alt="OmniSageAi" style={{ height: 32 }} />
              )}
            </IconButton>

            {!isMobileScreen && renderBusinessDropdown()}
            {isMobileScreen && !['accountant', 'manager'].includes(role) && renderBusinessDropdown()}
          </Box>

          {/* Right side - Notifications and Avatar */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* Notification Bell */}
            <IconButton
              onClick={handleNotificationPanel}
              color="inherit"
            >
              <Badge
                variant="dot"
                color="error"
                invisible={!hasNewNotification}
              >
                <Notifications />
              </Badge>
            </IconButton>

            {/* User Avatar */}
            <IconButton onClick={() => navigate('/profile')}>
              <Avatar
                color={isMobileScreen ? '#011638' : '#293056'}
                name={userName || 'User'}
                size="40"
                round
              />
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Notification Modal */}
      <Modal
        open={notificationActive}
        onClose={() => setNotificationActive(false)}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <Paper
          sx={{
            width: '90%',
            maxWidth: 600,
            height: '85vh',
            display: 'flex',
            flexDirection: 'column',
            borderRadius: 2
          }}
        >
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            p: 2,
            borderBottom: 1,
            borderColor: 'divider'
          }}>
            <Typography variant="h6" fontWeight="medium">
              Notifications
            </Typography>
            <IconButton
              onClick={() => setNotificationActive(false)}
              size="small"
            >
              <Close />
            </IconButton>
          </Box>
          <Box sx={{ flex: 1, overflow: 'hidden' }}>
            <Notification setNoticationActive={setNotificationActive} />
          </Box>
        </Paper>
      </Modal>
    </>
  );
};

export default Header;
