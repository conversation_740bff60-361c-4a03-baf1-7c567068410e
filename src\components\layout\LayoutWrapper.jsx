import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import Header from './Header';
import Sidebar from './Sidebar';

const LayoutWrapper = ({ children, className = '' }) => {
  const { isMobileScreen } = useAuth();

  return (
    <div className="h-screen flex flex-col overflow-hidden bg-gray-50">
      {/* Header */}
      <Header />
      
      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar - Hidden on mobile */}
        {!isMobileScreen && (
          <div className="flex-shrink-0">
            <Sidebar />
          </div>
        )}
        
        {/* Main Content */}
        <main className={`flex-1 overflow-auto bg-gray-50 ${className}`}>
          <div className="h-full p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default LayoutWrapper;
