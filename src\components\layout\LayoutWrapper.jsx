import React from 'react';
import { Box } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import Header from './Header';
import Sidebar from './Sidebar';

const LayoutWrapper = ({ children, className = '' }) => {
  const { isMobileScreen } = useAuth();

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      {/* Header */}
      <Header />

      {/* Main Content Area */}
      <Box
        sx={{
          display: 'flex',
          flex: 1,
          overflow: 'hidden',
          p: 2,
          backgroundColor: '#f5f7fa'
        }}
      >
        {/* Sidebar - Hidden on mobile */}
        {!isMobileScreen && (
          <Box sx={{ width: 257, mr: 2, flexShrink: 0 }}>
            <Sidebar />
          </Box>
        )}

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flex: 1,
            overflow: 'auto',
            height: '100%'
          }}
          className={className}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default LayoutWrapper;
