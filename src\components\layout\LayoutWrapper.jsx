import React from 'react';
import { Box } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import Header from './Header';
import Sidebar from './Sidebar';

const LayoutWrapper = ({ children, className = '' }) => {
  const { isMobileScreen } = useAuth();

  return (
    <Box sx={{ height: '100vh', display: 'flex', overflow: 'hidden' }}>
      {/* Sidebar - Hidden on mobile, positioned on the left */}
      {!isMobileScreen && (
        <Box sx={{ width: 257, flexShrink: 0, display: 'flex', flexDirection: 'column' }}>
          <Sidebar />
        </Box>
      )}

      {/* Right side with Header and Main Content */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
        {/* Header */}
        <Header />

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flex: 1,
            overflow: 'auto',
            p: 2,
            backgroundColor: '#f5f7fa',
            height: 'calc(100vh - 64px)' // Subtract header height (64px is standard MUI AppBar height)
          }}
          className={className}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default LayoutWrapper;
