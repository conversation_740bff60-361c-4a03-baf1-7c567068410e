import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import Header from './Header';
import Sidebar from './Sidebar';

const LayoutWrapper = ({ children, className = '' }) => {
  const { isMobileScreen } = useAuth();

  return (
    <div className="h-screen max-h-screen overflow-hidden">
      {/* Header */}
      <Header />

      {/* Main Content Area */}
      <div className="flex h-[calc(100vh-80px)] overflow-hidden px-4 py-4 bg-gray-50">
        {/* Sidebar - Hidden on mobile */}
        {!isMobileScreen && (
          <div className="w-64 mr-8 flex-shrink-0">
            <Sidebar />
          </div>
        )}

        {/* Main Content */}
        <main className={`flex-1 overflow-auto ${className}`}>
          <div className="h-full">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default LayoutWrapper;
